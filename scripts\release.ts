#!/usr/bin/env tsx

import { execSync } from "child_process";
import {
  askQuestion,
  askConfirmation,
  createReadlineInterface
} from "./utils.js";

const rl = createReadlineInterface();

/**
 * Complete release workflow for obsidian-plugin-config
 * 1. Update version
 * 2. Commit and push to GitHub
 * 3. Build and publish to NPM
 */
async function releaseWorkflow(): Promise<void> {
  console.log(`🚀 Obsidian Plugin Config - Complete Release Workflow`);
  console.log(`This will: update version → commit → push → publish NPM\n`);

  try {
    // Step 1: Ask for confirmation
    const shouldProceed = await askConfirmation(`Proceed with complete release workflow?`, rl);
    
    if (!shouldProceed) {
      console.log(`❌ Release cancelled`);
      return;
    }

    // Step 2: Update version
    console.log(`\n📋 Step 1/3: Updating version...`);
    execSync('tsx scripts/update-version-config.ts', { stdio: 'inherit' });

    // Step 3: Commit and push
    console.log(`\n📋 Step 2/3: Committing and pushing to GitHub...`);
    
    const commitMessage = await askQuestion(`Enter commit message for this release: `, rl);
    
    if (!commitMessage.trim()) {
      console.log(`❌ Commit message required`);
      return;
    }

    // Use acp script with the commit message
    process.env.COMMIT_MESSAGE = commitMessage.trim();
    execSync('tsx scripts/acp.ts', { stdio: 'inherit' });

    // Step 4: Build and publish NPM
    console.log(`\n📋 Step 3/3: Building and publishing to NPM...`);
    execSync('tsx scripts/build-npm.ts', { stdio: 'inherit' });

    console.log(`\n🎉 Release completed successfully!`);
    console.log(`\n📋 Next steps:`);
    console.log(`   1. npm install -g obsidian-plugin-config  # Update global package`);
    console.log(`   2. Test injection on target plugins`);

  } catch (error) {
    console.error(`\n❌ Release failed: ${error instanceof Error ? error.message : String(error)}`);
    throw error;
  }
}

/**
 * Main function
 */
async function main(): Promise<void> {
  try {
    await releaseWorkflow();
  } catch (error) {
    console.error(`💥 Error: ${error instanceof Error ? error.message : String(error)}`);
    process.exit(1);
  } finally {
    rl.close();
  }
}

// Run the script
main().catch(console.error);
