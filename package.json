{"name": "obsidian-plugin-config", "version": "1.1.8", "description": "Système d'injection pour plugins Obsidian autonomes", "type": "module", "main": "src/index.ts", "bin": {"obsidian-inject": "./bin/obsidian-inject.js"}, "license": "MIT", "keywords": ["obsidian", "obsidian-plugin", "typescript", "injection", "autonomous", "cli", "development-tools", "plugin-utilities"], "scripts": {"start": "yarn install && yarn run update-exports", "i": "yarn install && yarn run update-exports", "update-exports": "node scripts/update-exports.js", "ue": "node scripts/update-exports.js", "acp": "tsx scripts/acp.ts", "bacp": "tsx scripts/acp.ts -b", "update-version": "tsx scripts/update-version-config.ts", "v": "tsx scripts/update-version-config.ts", "build": "tsc -noEmit -skip<PERSON><PERSON><PERSON><PERSON><PERSON>", "dev": "tsx scripts/esbuild.config.ts", "real": "tsx scripts/esbuild.config.ts production real", "lint": "eslint . --ext .ts", "lint:fix": "eslint . --ext .ts --fix", "inject-path": "tsx scripts/inject-path.ts", "inject-prompt": "tsx scripts/inject-prompt.ts", "inject": "tsx scripts/inject-prompt.ts", "check-plugin": "tsx scripts/inject-path.ts --dry-run", "build-npm": "tsx scripts/build-npm.ts", "publish-npm": "tsx scripts/build-npm.ts", "help": "tsx scripts/help.ts", "h": "tsx scripts/help.ts"}, "exports": {".": "./src/index.ts", "./scripts/*": "./scripts/*", "./modals": "./src/modals/index.ts", "./tools": "./src/tools/index.ts", "./utils": "./src/utils/index.ts"}, "devDependencies": {"@types/eslint": "latest", "@types/node": "^22.15.26", "@types/semver": "^7.7.0", "@typescript-eslint/eslint-plugin": "latest", "@typescript-eslint/parser": "latest", "builtin-modules": "3.3.0", "dedent": "^1.6.0", "dotenv": "^16.4.5", "esbuild": "latest", "eslint": "latest", "eslint-import-resolver-typescript": "latest", "fs-extra": "^11.2.0", "jiti": "latest", "obsidian": "*", "obsidian-typings": "^3.9.5", "semver": "^7.7.2", "tsx": "^4.19.4", "typescript": "^5.8.2"}, "dependencies": {"@types/lodash": "^4.17.17", "@types/node": "^22.15.26", "@types/semver": "^7.7.0", "builtin-modules": "3.3.0", "dedent": "^1.6.0", "dotenv": "^16.4.5", "esbuild": "latest", "fs-extra": "^11.2.0", "lodash": "^4.17.21", "obsidian": "*", "obsidian-typings": "^3.9.5", "semver": "^7.7.2", "tsx": "^4.19.4", "typescript": "^5.8.2"}, "engines": {"npm": "please-use-yarn", "yarn": ">=1.22.0", "node": ">=16.0.0"}, "repository": {"type": "git", "url": "https://github.com/3C0D/obsidian-plugin-config.git"}, "author": "3C0D"}